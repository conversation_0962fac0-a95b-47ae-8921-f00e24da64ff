import SectionContainer from '@/src/components/layouts/SectionContainer'
import { InbaArticlesListSectionFragment } from '@/src/services/graphql'

type Props = {
  section: InbaArticlesListSectionFragment
}

/**
 * TODO Figma link
 */

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const InbaArticlesListSection = ({ section }: Props) => {
  // eslint-disable-next-line i18next/no-literal-string
  return <SectionContainer>Inba Articles List section - to be removed</SectionContainer>
}

export default InbaArticlesListSection
