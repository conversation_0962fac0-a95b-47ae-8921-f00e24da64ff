/**
 * This file was automatically generated by <PERSON>rap<PERSON>.
 * Any modifications made will be discarded.
 */
import graphql from "@strapi/plugin-graphql/strapi-admin";
import usersPermissions from "@strapi/plugin-users-permissions/strapi-admin";
import configSync from "strapi-plugin-config-sync/strapi-admin";
import meilisearch from "strapi-plugin-meilisearch/strapi-admin";
import { renderAdmin } from "@strapi/strapi/admin";

import customisations from "../../src/admin/app.tsx";

renderAdmin(document.getElementById("strapi"), {
  customisations,

  plugins: {
    graphql: graphql,
    "users-permissions": usersPermissions,
    "config-sync": configSync,
    meilisearch: meilisearch,
  },
});
