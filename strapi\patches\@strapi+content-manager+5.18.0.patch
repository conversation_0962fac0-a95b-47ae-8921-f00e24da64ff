diff --git a/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/Field.mjs b/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/Field.mjs
index 88c6e88..0e50319 100644
--- a/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/Field.mjs
+++ b/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/Field.mjs
@@ -28,7 +28,8 @@ const Wysiwyg = /*#__PURE__*/ React.forwardRef(({ hint, disabled, label, name, p
         const formattedFiles = files.map((f)=>({
                 alt: f.alternativeText || f.name,
                 url: prefixFileUrlWithBackendUrl(f.url),
-                mime: f.mime
+                mime: f.mime,
+                caption: f.caption || f.name
             }));
         insertFile(editorRef, formattedFiles);
         setMediaLibVisible(false);
diff --git a/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/utils/utils.mjs b/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/utils/utils.mjs
index 4f76601..939d23b 100644
--- a/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/utils/utils.mjs
+++ b/node_modules/@strapi/content-manager/dist/admin/pages/EditView/components/FormInputs/Wysiwyg/utils/utils.mjs
@@ -231,7 +231,7 @@ const insertFile = (editor, files)=>{
             editor.current.replaceSelection('\n');
         }
         if (file.mime.includes('image')) {
-            editor.current.replaceSelection(`![${file.alt}](${file.url})`);
+            editor.current.replaceSelection(`![${file.alt}](${file.url} "${file.caption}")`);
         } else {
             editor.current.replaceSelection(`[${file.alt}](${file.url})`);
         }
